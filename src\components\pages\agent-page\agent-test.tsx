"use client";

import { Mic } from "lucide-react";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PhoneInput } from "@/components/ui/phone-input";
import { useAgentConfigStore } from "@/store/agent-config";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useOutboundTrunks } from "@/hooks/use-sip-trunks";


const AgentTest = () => {
  const [phonenumber, setPhonenumber] = useState<string>("");
  const { config } = useAgentConfigStore();
  const [includePlus, setIncludePlus] = useState<boolean>(false);
  const [selectedTrunkId, setSelectedTrunkId] = useState<string>("");
  const { trunks: outboundTrunks, isLoading: isLoadingTrunks } = useOutboundTrunks();

  return (
    <div className="h-full flex items-center justify-center">
      <div className="flex gap-2 flex-col items-center">
        <Mic size={64} />
        <PhoneInput
          onChange={(value) => {
            setPhonenumber(value);
          }}
          defaultCountry="TR"
          countries={["TR"]}
        />

        <Select value={selectedTrunkId} onValueChange={setSelectedTrunkId}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder={isLoadingTrunks ? "Loading trunks..." : "Select outbound trunk"} />
          </SelectTrigger>
          <SelectContent>
            {outboundTrunks.map((trunk) => (
              <SelectItem key={trunk.sipTrunkId} value={trunk.sipTrunkId}>
                {trunk.name} ({trunk.address})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <div className="mt-3 flex items-center space-x-2">
          <Checkbox
            id="compare-previous"
            checked={includePlus}
            onCheckedChange={(checked) =>
              setIncludePlus(checked === true)
            }
          />
          <label
            htmlFor="compare-previous"
            className="text-sm text-muted-foreground"
          >
            + Dahil edilsin mi?
          </label>
        </div>
        <Button
          onClick={() => {
            if (!selectedTrunkId) {
              alert("Please select an outbound trunk");
              return;
            }
            fetch("/api/start-call", {
              method: "POST",
              headers: {
                Accept: "application/json",
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                metadata: {
                  ...config,
                },
                outbound_trunk_id: selectedTrunkId,
                phonenumber: includePlus === true ? "+" + phonenumber.replace("+", "") : phonenumber.replace("+", "")
              }),
            });
          }}
          className="w-full"
          disabled={!selectedTrunkId || isLoadingTrunks}
        >
          Test your agent
        </Button>
      </div>
    </div>
  );
};

export default AgentTest;
